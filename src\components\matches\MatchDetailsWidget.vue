<script setup lang="ts">
import { computed, ref, watch } from 'vue' // Added watch
import { storeToRefs } from 'pinia'
import { useMatchesStore } from '@/stores/matches'
import { useAuthStore } from '@/stores/auth'; // Added auth store import
import { useUserStore } from '@/stores/user'; // Added user store import
import type { Match as ApiMatch, Equipment as ApiEquipment } from '@/api/feathers-client'
import { fetchSunriseSunset, formatDateForApi, SunriseSunsetErrorMessages } from '@/services/sunriseSunset'
import { getWeatherForecast } from '@/services/weatherService' // Added weather service import
import WeatherIcon from './WeatherIcon.vue' // Import WeatherIcon

// ... other imports ...
import {
  Switch
} from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  Users,
  Navigation,
  Sunrise,
  Sunset,
  Thermometer,
  CloudSun,
  Sun,
  ShieldAlert,
  Award,
  Info as InfoIcon,
  Crosshair,
  Skull,
  X as XIcon, // Import XIcon
  Eye as EyeIcon // Import EyeIcon
} from 'lucide-vue-next'

// Define helper interface for API's age category structure
interface ApiAgeCategory {
  id: string | number;
  name?: string;
  short_name?: string;
  abbreviation?: string;
  min_age?: number;
  min?: number;
  max_age?: number;
  max?: number;
}

// Define helper interface for API's payment structure
interface ApiPaymentEntry {
  amount: number;
  currency: string;
  // any other fields from a payment entry
}

interface WeatherDataFromApi {
  hourly?: { // Kept for potential future use, though daily is primary now
    time: string[];
    temperature_2m: number[];
    relative_humidity_2m: number[];
    wind_speed_10m: number[];
  };
  daily?: {
    time: string[];
    weather_code?: number[]; // Added weather_code
    temperature_2m_max?: number[];
    temperature_2m_min?: number[];
    sunrise?: string[];
    sunset?: string[];
    // precipitation_sum?: number[]; // Removed as per recent service changes
  };
  // Include other top-level fields from WeatherData in weatherService if needed
  daily_units?: Record<string, string>;
  timezone?: string;
  // ... etc.
}

interface ExtendedMatchOptionalFields {
  distanceKm?: number;
  sunriseTime?: string;
  sunsetTime?: string;
  // temperatureCelsius?: number; // Will be fetched
  participantProgress?: number; // Client-side calculation/state

  // Ensure these camelCase fields are part of ExtendedMatch,
  // potentially sourced from ApiMatch or defined here.
  participantsCount?: number;
  competitionLevel?: string;
  international?: boolean;
  matchType?: string;

  // Snake_case fallbacks from API if not in ApiMatch's static type
  // or if data sometimes arrives with snake_case.
  competition_level?: string;
  is_international?: boolean;
  match_type?: string;
  participants_count?: number;

  // Typed arrays for fields that might be 'any[]' or loosely typed on ApiMatch
  payments?: ApiPaymentEntry[];
  ageCategories?: ApiAgeCategory[]; // This types `match.ageCategories`
}

type ExtendedMatch = ApiMatch & ExtendedMatchOptionalFields;

interface DisplayEquipmentCategory {
  id: string | number;
  name: string;
  short_name: string;
}

interface DisplayAgeCategory {
  id: string | number;
  name: string;
  short_name: string;
  min?: number | null;
  max?: number | null;
}

interface DisplayDataType {
  id: number;
  name: string;
  country?: string;
  city?: string;
  startDate?: string | Date;
  endDate?: string | Date;
  equipmentCategories: DisplayEquipmentCategory[];
  ageCategories: DisplayAgeCategory[];
  federation?: string;
  licenseRequired?: boolean;
  organizerName: string;
  competitionRank: string;
  competitionType: string;
  distanceKm: number;
  sunriseTime: string; // Will now be dynamically fetched
  sunsetTime: string; // Will now be dynamically fetched
  temperatureCelsius: number;
  priceAmount: number;
  priceCurrency: string;
  participantsCount: number; // Will use mockedRivalsCount
  participantProgress: number; // Will use mockedDifficultyPercentage
}

const props = defineProps<{
  match?: ApiMatch | null
}>()

const matchesStore = useMatchesStore()
const { currentMatch: storeCurrentMatch, isLoading, error } = storeToRefs(matchesStore)
const authStore = useAuthStore(); // Init auth store
const { user } = storeToRefs(authStore); // Get user from auth store
const userStore = useUserStore(); // Init user store
const { primaryPlayer } = storeToRefs(userStore); // Get primary player from user store

const isSignedUp = ref(false)
const isSignupLoading = ref(false)
const fetchedSunriseTime = ref<string | null>(null)
const fetchedSunsetTime = ref<string | null>(null)
const isFetchingSunTimes = ref(false)

// Add refs for weather data
const fetchedWeatherData = ref<WeatherDataFromApi | null>(null)
const isFetchingWeather = ref(false)
const weatherError = ref<string | null>(null)

// Cache for mocked data per match ID
const mockedDataCache = ref(new Map<number, { rivals: number; difficulty: number }>());

const currentMatch = computed<ExtendedMatch | null>(() => {
  const baseMatch = props.match || storeCurrentMatch.value;
  if (baseMatch) {
    return baseMatch as ExtendedMatch;
  }
  return null;
})

const getMockedDataForMatch = (matchId: number) => {
  if (!mockedDataCache.value.has(matchId)) {
    mockedDataCache.value.set(matchId, {
      rivals: Math.floor(Math.random() * 30) + 1,
      difficulty: Math.floor(Math.random() * 91) + 10, // 10-100%
    });
  }
  return mockedDataCache.value.get(matchId)!; // Safe due to the check above
};

const mockedRivalsCount = computed(() => {
  if (currentMatch.value && currentMatch.value.id != null) {
    return getMockedDataForMatch(currentMatch.value.id).rivals;
  }
  return 0; // Default if no match or ID
});

const mockedDifficultyPercentage = computed(() => {
  if (currentMatch.value && currentMatch.value.id != null) {
    return getMockedDataForMatch(currentMatch.value.id).difficulty;
  }
  return 0; // Default if no match or ID
});

const progressBarColorStyle = computed(() => {
  const percentage = mockedDifficultyPercentage.value;
  // Handle no match case for progress bar, or 0% explicitly
  if ((!currentMatch.value && percentage === 0) || percentage === 0) return 'bg-muted';
  if (percentage < 33) return 'bg-green-500';
  if (percentage < 66) return 'bg-yellow-500';
  return 'bg-red-500';
});

// const weatherIconComponent = computed(() => CloudSun); // Remove old computed

// New computed for weather code
const currentWeatherCode = computed<number | null>(() => {
  if (isFetchingWeather.value || weatherError.value || !fetchedWeatherData.value?.daily?.weather_code?.[0]) {
    return null; // Return null if loading, error, or no code
  }
  return fetchedWeatherData.value.daily.weather_code[0];
});

// Function that uses sunriseSunset service to fetch data
async function getSunriseSunsetData(latitude: number, longitude: number, date?: string) {
  if (!latitude || !longitude) {
    fetchedSunriseTime.value = SunriseSunsetErrorMessages.LOCATION_MISSING;
    fetchedSunsetTime.value = SunriseSunsetErrorMessages.LOCATION_MISSING;
    return;
  }

  isFetchingSunTimes.value = true;
  fetchedSunriseTime.value = SunriseSunsetErrorMessages.LOADING;
  fetchedSunsetTime.value = SunriseSunsetErrorMessages.LOADING;

  try {
    const result = await fetchSunriseSunset(latitude, longitude, date);

    if ('message' in result) {
      // It's an error
      fetchedSunriseTime.value = SunriseSunsetErrorMessages.API_ERROR;
      fetchedSunsetTime.value = SunriseSunsetErrorMessages.API_ERROR;
    } else {
      // It's a successful result
      fetchedSunriseTime.value = result.sunrise;
      fetchedSunsetTime.value = result.sunset;
    }
  } finally {
    isFetchingSunTimes.value = false;
  }
}

// Signup functionality
async function handleSignupClick() {
  console.log('handleSignupClick called, current isSignedUp:', isSignedUp.value)

  if (!currentMatch.value || !primaryPlayer.value) {
    console.error('No match or player available for signup')
    return
  }

  if (isSignupLoading.value) {
    console.log('Already loading, ignoring click')
    return
  }

  isSignupLoading.value = true
  try {
    if (isSignedUp.value) {
      // Unregister from match
      console.log('Unregistering from match...')
      await unregisterFromMatch()
    } else {
      // Register for match
      console.log('Registering for match...')
      await registerForMatch()
    }
  } catch (error) {
    console.error('Error toggling signup:', error)
  } finally {
    isSignupLoading.value = false
  }
}

async function registerForMatch() {
  console.log('🔄 registerForMatch() called')

  if (!currentMatch.value || !primaryPlayer.value) {
    console.error('❌ Missing match or player data in registerForMatch')
    console.log('  - currentMatch:', currentMatch.value)
    console.log('  - primaryPlayer:', primaryPlayer.value)
    return
  }

  const registrationData = {
    matchId: currentMatch.value.id,
    playerId: primaryPlayer.value.id,
    // Add basic required fields - you can expand these later
    styleDivision: primaryPlayer.value.equipmentCategory || 'recurve',
    ageDivision: 'senior', // Default for now
    genderDivision: primaryPlayer.value.sex || 'male'
  }

  console.log('📋 Registration data prepared:', registrationData)
  console.log('🌐 Calling matchesStore.createMatchRegistration...')

  try {
    const result = await matchesStore.createMatchRegistration(registrationData)
    console.log('✅ API call successful! Registration created:', result)
    return result
  } catch (error) {
    console.error('❌ API call failed:', error)
    throw error
  }
}

async function unregisterFromMatch() {
  // For now, we'll implement this as a simple toggle
  // In a real implementation, you'd call a delete/patch endpoint
  console.log('Unregistering from match - to be implemented')
  // Don't set isSignedUp here - let the watcher handle the state
}

// Check if user is already signed up for this match
async function checkSignupStatus() {
  console.log('Checking signup status...')
  console.log('Current match:', currentMatch.value?.id)
  console.log('Primary player:', primaryPlayer.value?.id)

  if (!currentMatch.value || !primaryPlayer.value) {
    console.log('Missing match or player, setting isSignedUp to false')
    isSignedUp.value = false
    return
  }

  try {
    // Find registrations for this match and player
    console.log('Fetching match registrations...')
    await matchesStore.findMatchRegistrations({
      query: {
        matchId: currentMatch.value.id,
        playerId: primaryPlayer.value.id
      }
    })

    console.log('Match registrations:', matchesStore.matchRegistrations)

    // Check if any registration exists
    const existingRegistration = matchesStore.matchRegistrations.find(
      reg => reg.matchId === currentMatch.value!.id && reg.playerId === primaryPlayer.value!.id
    )

    console.log('Existing registration found:', existingRegistration)
    isSignedUp.value = !!existingRegistration
    console.log('isSignedUp set to:', isSignedUp.value)
  } catch (error) {
    console.error('Error checking signup status:', error)
    isSignedUp.value = false
  }
}

// Watch for changes in currentMatch to fetch sunrise/sunset times
watch(currentMatch, (newMatch, oldMatch) => {
  // Reset fetched times on match change
  if (newMatch?.id !== oldMatch?.id) {
    fetchedSunriseTime.value = null;
    fetchedSunsetTime.value = null;
  }

  if (newMatch && newMatch.latitude != null && newMatch.longitude != null) {
    const apiDate = formatDateForApi(newMatch.startDate);
    getSunriseSunsetData(newMatch.latitude, newMatch.longitude, apiDate || undefined);
  } else {
    // Reset if no match or no lat/lng
    fetchedSunriseTime.value = newMatch ? SunriseSunsetErrorMessages.LOCATION_MISSING : null;
    fetchedSunsetTime.value = newMatch ? SunriseSunsetErrorMessages.LOCATION_MISSING : null;
  }
}, { immediate: true }); // immediate: true to run on initial load if currentMatch is already set

// Watch for changes in currentMatch to fetch weather data
watch(currentMatch, async (newMatch, oldMatch) => {
  if (newMatch?.id !== oldMatch?.id) {
    fetchedWeatherData.value = null;
    weatherError.value = null;
  }

  if (newMatch && newMatch.latitude != null && newMatch.longitude != null && newMatch.startDate) {
    isFetchingWeather.value = true;
    weatherError.value = null;
    try {
      const weatherDate = new Date(newMatch.startDate);
      fetchedWeatherData.value = await getWeatherForecast({
        latitude: newMatch.latitude,
        longitude: newMatch.longitude,
        date: weatherDate,
      });
    } catch (error) {
      console.error('Error fetching weather:', error);
      weatherError.value = 'Failed to load weather data.';
      fetchedWeatherData.value = null;
    } finally {
      isFetchingWeather.value = false;
    }
  } else {
    fetchedWeatherData.value = null;
    if (newMatch) {
        weatherError.value = 'Location or date missing for weather forecast.';
    }
  }
}, { immediate: true });

// Watch for changes in match or player to check signup status
watch([currentMatch, primaryPlayer], () => {
  if (currentMatch.value && primaryPlayer.value) {
    checkSignupStatus()
  } else {
    isSignedUp.value = false
  }
}, { immediate: true })

// Handle switch click manually
async function handleSwitchClick(event: Event) {
  console.log('=== SWITCH CLICKED ===')
  console.log('Event:', event)
  console.log('Current isSignedUp:', isSignedUp.value)
  console.log('Current match:', currentMatch.value?.id)
  console.log('Primary player:', primaryPlayer.value?.id)

  // Prevent default to stop automatic toggle
  event.preventDefault()
  event.stopPropagation()

  if (!currentMatch.value || !primaryPlayer.value) {
    console.error('❌ No match or player available for signup')
    return
  }

  if (isSignupLoading.value) {
    console.log('⏳ Already loading, ignoring click')
    return
  }

  console.log('🚀 Starting signup process...')
  isSignupLoading.value = true

  try {
    if (isSignedUp.value) {
      console.log('📤 Attempting to unregister...')
      await unregisterFromMatch()
      console.log('✅ Unregistration successful, setting isSignedUp to false')
      isSignedUp.value = false
    } else {
      console.log('📥 Attempting to register...')
      await registerForMatch()
      console.log('✅ Registration successful, setting isSignedUp to true')
      isSignedUp.value = true
    }
  } catch (error) {
    console.error('❌ Error during signup process:', error)
  } finally {
    isSignupLoading.value = false
    console.log('🏁 Signup process completed')
  }
}

function deg2rad(deg: number) {
  return deg * (Math.PI / 180);
}

const displayData = computed<DisplayDataType | null>(() => {
  const match = currentMatch.value;
  if (!match) return null;

  // Calculate distance
  let calculatedDistanceKm = match.distanceKm ?? 100; // Default or existing value from match
  if (user.value?.player?.lat != null && user.value?.player?.lng != null && match.latitude != null && match.longitude != null) {
    const R = 6371; // Radius of the Earth in km
    const dLat = deg2rad(match.latitude - user.value.player.lat);
    const dLon = deg2rad(match.longitude - user.value.player.lng);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(user.value.player.lat)) *
      Math.cos(deg2rad(match.latitude)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    calculatedDistanceKm = Math.round(R * c); // Rounded to the nearest integer
  }

  let priceAmount = 0;
  let priceCurrency = 'PLN';
  // Use optional chaining and check if payments array exists and has entries
  if (match.payments && match.payments.length > 0) {
    const firstPayment = match.payments[0]; // Now typed as ApiPaymentEntry | undefined
    if (firstPayment && typeof firstPayment.amount === 'number') {
      priceAmount = firstPayment.amount / 100; // Assuming amount is in cents
    }
    if (firstPayment && typeof firstPayment.currency === 'string') {
      priceCurrency = firstPayment.currency;
    }
  }

  const equipmentCategories: DisplayEquipmentCategory[] = (Array.isArray(match.equipmentCategories)
    ? match.equipmentCategories.map((apiCat: ApiEquipment) => {
        const nameStr = apiCat.name || 'Equipment';
        // Explicitly cast apiCat to include optional short_name/code if not on ApiEquipment type
        const typedApiCat = apiCat as ApiEquipment & { short_name?: string; code?: string };
        return {
          id: apiCat.id,
          name: nameStr,
          short_name: typedApiCat.short_name || typedApiCat.code || nameStr.substring(0, 3).toUpperCase() || 'N/A',
        };
      })
    : []);

  // match.ageCategories is now typed as ApiAgeCategory[] via ExtendedMatch
  const ageCategories: DisplayAgeCategory[] = (Array.isArray(match.ageCategories)
    ? match.ageCategories.map((apiCat: ApiAgeCategory) => {
        const nameStr = apiCat.name || 'Category';
        return {
          id: apiCat.id || Date.now() + Math.random(), // Fallback ID
          name: nameStr,
          short_name: apiCat.short_name || apiCat.abbreviation || nameStr.substring(0, 3).toUpperCase() || 'N/A',
          min: apiCat.min_age ?? apiCat.min ?? null,
          max: apiCat.max_age ?? apiCat.max ?? null,
        };
      })
    : []);

  const organizerName = match.organizer?.name ?? 'N/A';

  let temperatureDisplay: number | string = 'N/A';
  if (isFetchingWeather.value) {
    temperatureDisplay = 'Loading...';
  } else if (weatherError.value) {
    temperatureDisplay = 'Error';
  } else if (fetchedWeatherData.value) {
    if (fetchedWeatherData.value.daily?.temperature_2m_max?.[0] !== undefined) {
      temperatureDisplay = fetchedWeatherData.value.daily.temperature_2m_max[0];
    } else if (fetchedWeatherData.value.hourly?.temperature_2m?.[0] !== undefined) {
      // Attempt to find a temperature around noon for hourly, or just take the first one
      const noonIndex = fetchedWeatherData.value.hourly.time.findIndex(t => t.endsWith('T12:00'));
      temperatureDisplay = fetchedWeatherData.value.hourly.temperature_2m[noonIndex !== -1 ? noonIndex : 0];
    } else {
      temperatureDisplay = 'N/A';
    }
  }

  const result: DisplayDataType = {
    id: match.id,
    name: match.name,
    country: match.country,
    city: match.city,
    startDate: match.startDate,
    endDate: match.endDate,
    equipmentCategories: equipmentCategories,
    ageCategories: ageCategories,
    federation: match.federation,
    licenseRequired: match.licenseRequired,
    organizerName,

    // Access properties directly from 'match' (ExtendedMatch)
    competitionRank: match.competitionLevel ?? match.competition_level ?? 'Local',
    competitionType: (match.international ?? match.is_international) ? 'International' : (match.matchType ?? match.match_type ?? 'N/A'),

    distanceKm: calculatedDistanceKm, // Assign the number value
    sunriseTime: fetchedSunriseTime.value || (isFetchingSunTimes.value ? 'Loading...' : (match.sunriseTime ?? '06:00')),
    sunsetTime: fetchedSunsetTime.value || (isFetchingSunTimes.value ? 'Loading...' : (match.sunsetTime ?? '18:00')),
    temperatureCelsius: typeof temperatureDisplay === 'number' ? temperatureDisplay : (match.temperatureCelsius ?? 20), // Use fetched temperature

    priceAmount,
    priceCurrency,

    participantsCount: mockedRivalsCount.value, // Use cached-per-match value
    participantProgress: mockedDifficultyPercentage.value, // Use cached-per-match value
  };
  return result;
});

const ageCategoryFor45 = computed(() => {
  if (!displayData.value || !displayData.value.ageCategories) return null;
  return displayData.value.ageCategories.find(cat => {
    const minAge = cat.min;
    const maxAge = cat.max;
    // Ensure minAge and maxAge are treated as numbers for comparison
    const numMinAge = (typeof minAge === 'string' ? parseInt(minAge, 10) : minAge) ?? null;
    const numMaxAge = (typeof maxAge === 'string' ? parseInt(maxAge, 10) : maxAge) ?? null;

    if (numMinAge != null && numMaxAge != null) {
      return numMinAge <= 45 && numMaxAge >= 45;
    }
    if (numMinAge != null) {
      return numMinAge <= 45;
    }
    if (numMaxAge != null) {
      return 45 <= numMaxAge;
    }
    return false; // Default to false if no clear range defined that includes 45
  }) || null;
});

const formatDate = (dateString?: string | Date) => {
  if (!dateString) return 'N/A'
  const date = new Date(dateString);
  return date.toLocaleDateString('pl-PL', { year: 'numeric', month: '2-digit', day: '2-digit' });
};

const matchDurationDays = computed(() => {
  if (!displayData.value?.startDate || !displayData.value?.endDate) return 'N/A';
  const start = new Date(displayData.value.startDate);
  const end = new Date(displayData.value.endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.max(1, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  return `${diffDays} ${diffDays === 1 ? 'day' : 'days'}`;
});

const getFlagUrl = (countryCode?: string) => {
  if (!countryCode) return '';
  return `/flags/${countryCode.toLowerCase()}.svg`;
};

const handleCloseDetails = () => {
  matchesStore.clearSelectedMatch()
}

</script>

<template>
  <TooltipProvider>
    <div v-if="isLoading" class="p-4 text-center">Loading match details...</div>
    <div v-else-if="error" class="p-4 text-center text-red-500">Error loading match: {{ error.message }}</div>
    <div v-else-if="displayData" class="p-4 space-y-4 text-sm">
      <!-- Header: Sign Up & Icons -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <Switch
            id="signup-toggle"
            :checked="isSignedUp"
            :disabled="isSignupLoading || !primaryPlayer"
            @click="handleSwitchClick"
            class="data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-red-300"
          />
          <label for="signup-toggle" class="font-semibold text-lg">
            {{ isSignupLoading ? 'LOADING...' : 'SIGN UP' }}
          </label>
        </div>
        <div class="flex items-center space-x-1.5">
          <Button variant="ghost" size="icon" class="w-7 h-7">
            <EyeIcon class="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="icon" class="w-7 h-7" @click="handleCloseDetails">
            <XIcon class="w-5 h-5" />
          </Button>
        </div>
      </div>

      <!-- Match Title & Basic Info -->
      <div>
        <h2 class="text-xl font-semibold leading-tight mb-1">
          {{ displayData.name }}
        </h2>
        <div class="flex items-center space-x-2 text-muted-foreground">
          <img v-if="displayData.country" :src="getFlagUrl(displayData.country)" :alt="displayData.country" class="w-4 h-auto" />
          <span>{{ displayData.city }}</span>
          <span>{{ formatDate(displayData.startDate) }}</span>
          <span>| SO | {{ matchDurationDays }}</span>
          <Award class="w-4 h-4 text-red-500" />
          <ShieldAlert class="w-4 h-4 text-blue-500" />
        </div>
      </div>

      <!-- Icons Row & Price -->
      <div class="flex items-center justify-between text-muted-foreground">
        <div class="flex items-center space-x-3">
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Navigation class="w-4 h-4" />
              <span>{{ displayData.distanceKm }} km</span>
            </TooltipTrigger>
            <TooltipContent>Distance to event</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Sunrise class="w-4 h-4" />
              <span>{{ displayData.sunriseTime }}</span>
            </TooltipTrigger>
            <TooltipContent>Sunrise</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Sunset class="w-4 h-4" />
              <span>{{ displayData.sunsetTime }}</span>
            </TooltipTrigger>
            <TooltipContent>Sunset</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <Thermometer class="w-4 h-4" />
              <span>
                {{ typeof displayData.temperatureCelsius === 'number' ? displayData.temperatureCelsius.toFixed(0) + '°C' : displayData.temperatureCelsius }}
              </span>
            </TooltipTrigger>
            <TooltipContent>
              Temperature
              <span v-if="weatherError"> ({{ weatherError }})</span>
              <span v-else-if="isFetchingWeather"> (Loading...)</span>
              <span v-else-if="fetchedWeatherData?.daily?.temperature_2m_min?.[0] !== undefined && fetchedWeatherData?.daily?.temperature_2m_max?.[0] !== undefined">
                (Min: {{ fetchedWeatherData.daily.temperature_2m_min[0].toFixed(0) }}°C Max: {{ fetchedWeatherData.daily.temperature_2m_max[0].toFixed(0) }}°C)
              </span>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger class="flex items-center space-x-1">
              <!-- Use WeatherIcon component -->
              <WeatherIcon v-if="currentWeatherCode !== null" :weather-code="currentWeatherCode" :size="16" />
              <span v-else-if="isFetchingWeather">...</span>
              <CloudSun v-else class="w-4 h-4" /> <!-- Fallback icon -->
            </TooltipTrigger>
            <TooltipContent>
              Weather
              <span v-if="weatherError"> ({{ weatherError }})</span>
              <span v-else-if="isFetchingWeather"> (Loading...)</span>
              <span v-else-if="currentWeatherCode === null"> (N/A)</span>
            </TooltipContent>
          </Tooltip>
        </div>
        <Badge variant="outline" class="text-base px-3 py-1">
          {{ displayData.priceAmount }} {{ displayData.priceCurrency }}
        </Badge>
      </div>

      <Separator />

      <!-- Competitors/Rivals -->
      <div>
        <h3 class="font-semibold mb-2">Competitors/Rivals</h3>
        <div class="flex flex-wrap gap-2 mb-2">
          <Tooltip>
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">CU</Badge>
            </TooltipTrigger>
            <TooltipContent>Compound Unlimited (Mocked)</TooltipContent>
          </Tooltip>
          <Tooltip v-if="ageCategoryFor45">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ ageCategoryFor45.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>{{ ageCategoryFor45.name }} (Suitable for age 45)</TooltipContent>
          </Tooltip>
          <Tooltip v-else>
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">N/A</Badge>
            </TooltipTrigger>
            <TooltipContent>No specific age category for 45 found</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">M</Badge>
            </TooltipTrigger>
            <TooltipContent>Male (Mocked)</TooltipContent>
          </Tooltip>
        </div>
        <div class="flex items-center space-x-3 text-muted-foreground">
          <Users class="w-4 h-4 flex-shrink-0" />
          <span class="mr-2 flex-shrink-0">{{ displayData.participantsCount }}</span>

          <Crosshair class="w-4 h-4 flex-shrink-0" />
          <span class="mr-2 flex-shrink-0">{{ displayData.participantProgress?.toFixed(0) }}%</span>

          <Skull class="w-5 h-5 flex-shrink-0" :class="[progressBarColorStyle.includes('red') ? 'text-red-500' : progressBarColorStyle.includes('yellow') ? 'text-yellow-500' : 'text-green-500']" />
          <div class="flex-1 bg-muted rounded-full h-2.5 min-w-[50px]">
            <div class="h-2.5 rounded-full" :class="progressBarColorStyle" :style="{ width: displayData.participantProgress + '%' }"></div>
          </div>
        </div>
      </div>

      <Separator />

      <!-- Equipment Categories -->
      <div v-if="displayData.equipmentCategories && displayData.equipmentCategories.length > 0">
        <h3 class="font-semibold mb-2">Equipment Categories</h3>
        <div class="flex flex-wrap gap-2">
          <Tooltip v-for="cat in displayData.equipmentCategories" :key="cat.id">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ cat.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>{{ cat.name }}</TooltipContent>
          </Tooltip>
        </div>
      </div>

      <!-- Age Categories -->
      <div v-if="displayData.ageCategories && displayData.ageCategories.length > 0">
        <h3 class="font-semibold mb-2 mt-3">Age Categories</h3>
        <div class="flex flex-wrap gap-2">
          <Tooltip v-for="cat in displayData.ageCategories" :key="cat.id">
            <TooltipTrigger>
              <Badge variant="secondary" class="cursor-default">{{ cat.short_name }}</Badge>
            </TooltipTrigger>
            <TooltipContent>
              <span v-if="cat.min && cat.max">Age: {{ cat.min }} - {{ cat.max }}</span>
              <span v-else-if="cat.min">Age: {{ cat.min }}+</span>
              <span v-else-if="cat.max">Age: up to {{ cat.max }}</span>
              <span v-else>{{ cat.name }}</span> <!-- Fallback to name if no min/max -->
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      <Separator />

      <!-- Info Section -->
      <div>
        <h3 class="font-semibold mb-3 flex items-center">
          <InfoIcon class="w-5 h-5 mr-2 text-muted-foreground" />
          Info
        </h3>
        <div class="grid grid-cols-[max-content_1fr] gap-x-4 gap-y-1.5 text-muted-foreground">
          <span class="font-medium text-foreground">Organizer:</span>
          <span>{{ displayData.organizerName }}</span>

          <span class="font-medium text-foreground">Competition Rank:</span>
          <span>{{ displayData.competitionRank }}</span>

          <span class="font-medium text-foreground">Competition Type:</span>
          <span>{{ displayData.competitionType }}</span>

          <span class="font-medium text-foreground">Federation:</span>
          <span>{{ displayData.federation || 'N/A' }}</span>

          <span class="font-medium text-foreground">License:</span>
          <span>{{ displayData.licenseRequired ? 'Required' : 'Not Required' }}</span>
        </div>
      </div>
    </div>

  </TooltipProvider>
</template>

<style scoped>
/* Add any specific styles if needed, Tailwind should cover most */
</style>
